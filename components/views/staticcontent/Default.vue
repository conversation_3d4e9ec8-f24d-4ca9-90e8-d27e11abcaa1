<template>
	<Body :class="{'page-landing-menu': landingMenu && !landingMenuSpecial, 'page-landing-menu-special': landingMenuSpecial && !landingMenu}" />
	<BaseMetaSeo :data="item" />
	<div v-if="item?.items" class="landing-main" :style="{backgroundColor: item.color_1 ? item.color_1 : null, color: item.color_2 ? item.color_2 : null}">
		<div :class="{'landing-wrapper': item?.items?.some(obj => obj.layout != 'landing_widget' && obj.layout != 'landing_widget_content' && obj.layout != 'landing_widget_braun')}">
			<template v-for="item in item.items" :key="item.layout">
				<BaseStaticcontentLayout :item="item" />
			</template>
		</div>
	</div>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const router = useRouter();
	const {emit} = useEventBus();
	const endpoints = useEndpoints();
	const gtm = useGtmBB();
	const {generateThumbs} = useImages();
	const item = ref([]);
	let landingMenuSpecial = ref(false);
	let landingMenu = ref(false);

	//rwd
	const {mobileBreakpoint} = inject('rwd');

	await useApi('/api/nuxtapi/staticcontent/pages/', {
		method: 'POST',
		body: {
			slug: route.path
		}
	}).then(res => {
		if(res?.success && res?.data[0]) {
			let page = res?.data[0];

			//special slider
			let landingProductsSliderCount = 0
			for (let index = 0; index < page?.items?.length; index++) {
				const currentItem = page.items[index];
				const previousItem = index > 0 ? page.items[index - 1] : null;

				if (currentItem?.layout === 'landing_products_slider') {
					landingProductsSliderCount++;
					if (previousItem?.layout === 'landing_products_slider_promo') {
						const promoElements = previousItem.elements;

						if (promoElements && promoElements.length > 0) {
							generateThumbs({
								data: promoElements,
								preset: 'staticcontentCatalogPromo'
							}).then(() => {
								const { image_thumb, image2_thumb, url_without_domain } = promoElements[0];

								currentItem.elements = currentItem.elements || {};

								if (image_thumb) {
									currentItem.image_thumb = image_thumb;
								}
								if (image2_thumb) {
									currentItem.image2_thumb = image2_thumb;
								}
								if (url_without_domain) {
									currentItem.url_without_domain = url_without_domain;
								}
							});
						}
					}
				}

				if (landingProductsSliderCount % 2 !== 0 && landingProductsSliderCount > 0) {
					let lastIndex = -1;
					for (let i = page?.items?.length - 1; i >= 0; i--) {
						if (page?.items[i]?.layout === 'landing_products_slider') {
							lastIndex = i;
							break;
						}
					}

					if (lastIndex !== -1) {
						page.items[lastIndex]['odd'] = true;
					}
				}
			}

			item.value = page;
		}
	})

	onMounted(async () => {
		//menu
		const menuSpecial = item?.value?.items?.some(obj => obj.layout === 'landing_menu');
		const menuData = item?.value?.items?.filter(item => item?.elements[0]?.menu_visible === "1")
		.map(item => ({title: item?.elements[0]?.menu_title || item?.elements[0]?.title, position: item?.elements[0]?.position_h}));

		if(menuSpecial && !mobileBreakpoint.value) {
			landingMenuSpecial.value = true;
			emit('landingMenuSpecial');
		} else if(menuData?.length) {
			landingMenu.value = true;
			emit('landingMenu', menuData);
		}

		//gtm
		gtm.pageView({
			url: item?.value?.url || '',
			title: item?.value?.title || '',
		});
	})

	// generate menu from page items
	const menu = computed(() => {
		if (!item.value) return [];

		let menuItems = [];
		if (item.value?.items) {
			item.value.items.forEach(item => {
				if (item.elements) {
					item.elements.forEach(element => {
						if (!element.menu_visible || element.menu_visible == '0') return;
						menuItems.push({
							title: element.menu_title ? element.menu_title : element.title,
							id: element.id,
							position: item.position,
							layout: element.layout_code,
						});
					});
				}
			});
		}

		// append menu items to page object
		item.menu = menuItems;
		emit('data', item.value);

		return menuItems;
	});

	// provide data to child components
	provide('baseStaticcontentPageData', {
		menu: menu.value,
	});

	onUnmounted(() => {
		landingMenuSpecial.value = false;
		landingMenu.value = false;
	})
</script>

<style lang="less" scoped>
	.landing-main{margin-bottom: -1px;}
	.landing-wrapper{
		width: auto; max-width: 1480px; margin: 0 auto; text-align: center;
		:deep(.ln-section-special-slider:nth-of-type(odd)){margin-right: 0;}

		@media (max-width: @l){width: auto; max-width: unset; margin: 0 70px;}
		@media (max-width: @t){margin: 0 30px;}
		@media (max-width: @m){margin: 0 20px;}
		@media (max-width: @ms){margin: 0 15px;}
	}
</style>
