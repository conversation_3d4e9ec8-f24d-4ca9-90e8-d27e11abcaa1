<template>
	<BaseCatalogCategory v-slot="{item: category, contentType, queryParams}" :seo="true" thumb-preset="promoCategory" @loadCategory="loadCategory">
		<template v-if="contentType != 'search' && contentType != 'detail'">
			<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" class="c-bc" />
		</template>
	</BaseCatalogCategory>
</template>

<script setup>
	const config = useAppConfig();
	const labels = useLabels();
	const route = useRoute();
	const {relative} = useUrl();
	const mobileFiltersActive = ref(false);
	const gtm = useGtmBB();
	const {bus, emit} = useEventBus();

	//rwd
	const {mobileBreakpoint, mobileSmallBreakpoint} = inject('rwd');

	//show/hide hide_description
	const contentRef = ref(null);
	const isLongText = ref(false);

	//catalog header background
	let catalogMain = ref(null);
	const catalogHeader = ref(null);
	let catalogHeaderHeight = ref(null);

	const catalogMainBg = computed(() => {
		if (catalogMain.value && catalogHeaderHeight.value) {
			const catalogMainHeight = catalogMain.value.offsetHeight;
			return catalogMainHeight > catalogHeaderHeight.value;
		}
		return false;
	});

	watchEffect(() => {
		//show/hide hide_description
		if (contentRef.value) {
			const contentText = contentRef.value.textContent;
			const tempElement = document.createElement('div');
			tempElement.style.position = 'absolute';
			tempElement.style.left = '-9999px';
			tempElement.style.visibility = 'hidden';
			tempElement.style.width = `${contentRef.value.offsetWidth}px`;
			tempElement.innerHTML = contentText;
			tempElement.style.fontSize = window.getComputedStyle(contentRef.value).getPropertyValue('font-size');
			tempElement.style.lineHeight = window.getComputedStyle(contentRef.value).getPropertyValue('line-height');
			document.body.appendChild(tempElement);

			const contentHeight = tempElement.offsetHeight;

			isLongText.value = contentHeight > 25;

			document.body.removeChild(tempElement);
		}

		//catalog header background
		if (catalogHeader.value) {
			catalogHeaderHeight.value = catalogHeader.value.offsetHeight + 100;
		}
	});

	//special categories button
	let categoriesSpecialNumber = 10;
	let categoriesSpecialToggle = ref(false);

	//flyout shop extra info
	function flyoutExtraInfo() {
		document.body.classList.add('flyout-page', 'cd-flyout-active');
		emit('flyoutUpdate', {'flyout_title': labels.get('shop_extra_info_flyout_title'), 'flyout_content': labels.get('shop_extra_info_flyout_content')});
	}

	//open mobile filter popup
	function openMobileFilter() {
		document.body.classList.add('active-filters');
		mobileFiltersActive.value = true;
	}

	//close mobile filter popup
	function closeMobileFilter() {
		document.body.classList.remove('active-filters');
		mobileFiltersActive.value = false;
	}

	provide('closeMobileFilter', closeMobileFilter);

	onUnmounted(() => {
		document.body.classList.remove('active-filters');
	});

	//XXX verification
	let xxxVisibility = ref(null);
	function verificationXXX(title, value) {
		emit('xxx', {'title': title, 'value': value});
	}
	function hasCookie(cookieName, value) {
		if (typeof document !== 'undefined') {
			const cookies = document.cookie.split(';');
			for (let i = 0; i < cookies.length; i++) {
				const cookie = cookies[i].trim();
				if (cookie.startsWith(cookieName + '=')) {
					xxxVisibility.value = false;
					return true;
				} else {
					xxxVisibility.value = true;
				}
			}
		}
		return false;
	}

	const objectToQueryString = (obj) => {
		return Object.entries(obj)
			.filter(([key, value]) => {
				// Include the key-value pair only if the value is not an empty array
				return !Array.isArray(value) || (Array.isArray(value) && value.length > 0);
			})
			.map(([key, value]) => {
				return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
			})
			.join('&');
	};

	let gtmCategoryData = null;
	let loadedCategoryData = ref(null);
	function loadCategory(value) {
		if(value) loadedCategoryData.value = value;

		//gtm
		const transformedData = {
			'list_title': value?.item?.title ? value.item.title : null,
			'list_code': value?.item?.code ? value.item.code : null,
		};
		gtmCategoryData = transformedData;

		//xxx
		if(value?.item?.xxx) {
			hasCookie('xxx_verification');
			if(xxxVisibility.value == true) {
				verificationXXX();
			}
		}
	}

	watch(
		() => bus?.value,
		() => {
			if(bus?.value?.event == 'xxx_verificated') {
				hasCookie('xxx_verification');
				xxxVisibility.value = false;
			}
		}
	);

	function loadProducts(value) {
		if(value?.items?.length) gtm.viewItemList(value.items, gtmCategoryData);
		if(!loadedCategoryData?.value?.item?.url) return;
		const queryString = objectToQueryString(route?.query);
		const urlValue = queryString ? loadedCategoryData?.value?.item?.url + '?' + queryString : loadedCategoryData?.value?.item?.url;
		if(loadedCategoryData.value) {
			gtm.pageView({
				url: urlValue || '',
				title: loadedCategoryData?.value?.item?.title || '',
			});
		}
	}
	function loadProductsWidget(value) {
		if(value?.items?.length) {
			gtm.viewItemList(value.items, value.data);
		}
	}
	function loadPagination() {
		const queryString = objectToQueryString(route?.query);
		const urlValue = queryString ? loadedCategoryData?.value?.item?.url + '?' + queryString : loadedCategoryData?.value?.item?.url;

		gtm.pageView({
			url: urlValue || '',
			title: loadedCategoryData?.value?.item?.title || '',
		});
	}

	function loadRotatorItems(value) {
		if (value?.items?.length) {
			value?.items.forEach((item, index) => {
				gtm.viewPromotion({
					creative_name: item.title || '',
					creative_slot: value.creativeSlot + (index + 1),
					promotion_id: item.code || '',
					promotion_name: item.title || '',
				});
			});
		}
	}
</script>

<style lang="less">
	.page-catalog-lvl2{
		&.fixed-header{
			.c-toolbar{top: 64px;}
			.c-filters-section{height: calc(~"100vh - 91px"); top: 91px;}
		}
		.page-wrapper{overflow: unset;}

		@media (max-width: @t){
			&.fixed-header{
				.c-toolbar{top: 50px;}
				.c-filters-section{height: calc(~"100vh - 72px"); top: 72px;}
			}
		}
		@media (max-width: @m){
			&.fixed-header{
				.c-toolbar{top: 56px;}
				.c-filters-section{height: auto; top: unset;}
			}
		}
	}

	.c-main{
		position: relative;
		&.lvl2{
			border-bottom: 1px solid @separatorColor;
			&.bg:before{.pseudo(auto,160px); background: linear-gradient(180deg, rgba(248,249,250,0) 0%, #F3F6FA 100%); bottom: 0; left: 0; right: 0; z-index: -1;}
		}

		@media (max-width: @t){
			&.lvl2.bg:before{height: 110px;}
		}
		@media (max-width: @m){
			&.lvl2{
				border: none;
				&.bg:before{content: none;}
			}
		}
	}
	.c-bc .bc-item:first-child{display: none;}
	.c-header{position: relative;}
	.c-title{
		padding: 0 0 15px; font-size: 30px; line-height: 1.2; font-weight: 600;
		&.special{padding-bottom: 6px;}

		@media (max-width: @t){font-size: 26px;}
		@media (max-width: @m){
			font-size: 22px;
			&.special{padding-bottom: 4px;}
		}
	}
	.c-title-counter{
		font-size: 14px; line-height: 1.4; color: @inputTextColor;

		@media (max-width: @m){font-size: 12px;}
	}
	.c-subtitle{
		font-size: 22px; line-height: 1.3; font-weight: 600; padding-bottom: 12px;

		@media (max-width: @t){font-size: 20px; padding-bottom: 10px;}
	}

	.c-categories-wrapper{
		margin-bottom: 40px;
		&.special{margin-bottom: 20px;}

		@media (max-width: @t){
			margin-bottom: 30px;
			&.special{margin-bottom: 10px;}
		}
	}
	.c-categories-title{
		padding-bottom: 20px;

		@media (max-width: @t){padding-bottom: 15px;}
	}
	.c-categories{
		display: flex; flex-wrap: wrap; width: calc(~"100% - -10px"); margin-left: -5px; padding: 0; list-style: none; position: relative;
		&.active{
			.c-category{display: block;}
			.c-category-btn{
				&:after{top: 9px; .rotate(180deg);}
				.more{display: none;}
				.less{display: inline-block;}
			}
		}

		@media (max-width: @ms){display: block; width: 100%; margin: 0;}
	}
	.c-category{
		display: block; width: calc(~"100% / 5 - 10px"); margin: 0 5px 10px; font-size: 0; line-height: 0;
		&:not(:nth-child(-n+10)) {display: none;}
		&>a{
			display: flex; align-items: center; min-height: 74px; padding: 1px 15px; background: @white; border: 1px solid @borderColor; font-size: 15px; line-height: 1.2; color: @textColor; font-weight: 600; text-decoration: none; position: relative; transition: border-color 0.3s, color 0.3s;
			&>figure{
				display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 70px; height: 70px; margin-right: 10px;
				&>img{width: auto; height: auto; max-height: 100%; max-width: 100%; display: block;}
			}
			@media (min-width: @t){
				&:hover{border-color: @hoverBorderColor; color: @blue; text-decoration: none;}
			}
		}

		@media (max-width: @t){
			width: calc(~"100% / 4 - 10px");
			&:not(:nth-child(-n+10)) {display: block;}
			&:not(:nth-child(-n+8)) {display: none;}
			&>a{
				min-height: 40px; font-size: 14px;
				&>figure{width: 60px; height: 60px;}
				&:hover{color: @textColor;}
			}
		}
		@media (max-width: @m){
			width: calc(~"100% / 2 - 10px");
			&:not(:nth-child(-n+8)) {display: block;}
			&>a{
				min-height: 62px; font-size: 15px;
				&>figure{width: 56px; height: 56px; margin-right: 12px;}
			}
		}
		@media (max-width: @ms){width: 100%; margin: 0 0 -1px;}
	}
	.c-category-btn-container{
		display: flex; justify-content: flex-end; width: 100%; padding: 3px 5px 0;

		@media (max-width: @m){display: none!important;}
	}
	.c-category-btn{
		display: block; margin: 0; padding: 0 13px 0 0; font-size: 15px; line-height: 1.4; color: @blue; font-weight: 600; position: relative; cursor: pointer; z-index: 1;
		&:after{.icon-arrow-down(); font: 7px/7px @fonti; font-weight: 600; color: @blue; position: absolute; top: 8px; right: 0;}
		.less{display: none;}
		@media (min-width: @t){
			&:hover{text-decoration: underline;}
		}
	}

	.c-filters-section {
		width: 265px;
		@media (max-width: @t) {width: 205px;}
		@media (max-width: @m) {
			&.active{width: auto;}
		}
	}

	.c-bottom{
		padding: 45px 0; border-top: 1px solid @borderColor;

		@media (max-width: @t){padding: 30px 0;}
		@media (max-width: @m){padding: 20px 0 15px;}
	}
	.c-bottom-title{
		font-size: 20px; line-height: 1.2; padding: 0 0 5px;

		@media (max-width: @t){font-size: 18px;}
	}

	.c-items-row{
		display: flex; align-items: flex-start; padding: 0 0 50px;

		@media (max-width: @t){padding-bottom: 30px;}
	}
	.c-items-col{flex-grow: 1;}
	#items_catalog_layout{
		position: relative;
		&:before{.pseudo(auto,auto); background: rgba(255,255,255,0.5); top: -1px; bottom: -1px; left: -1px; right: -1px; z-index: -11; opacity: 0; .transition(opacity);}
		&.loading:before{opacity: 1; z-index: 111;}
	}
	.c-items{display: flex; flex-wrap: wrap; width: calc(~"100% - -20px"); margin-left: -10px; overflow-x: hidden;}
	.ci-counter{font-size: 12px; color: #7891A9;}

	.c-load-more-container{
		display: flex; flex-direction: column; align-items: center; margin: 10px 0 0;
		.btn{min-width: 220px; margin-top: 0;}

		@media (max-width: @m){
			.btn{min-width: 180px;}
		}
	}
	.c-load-before-container{
		margin: 0 0 20px;

		@media (max-width: @m){margin-bottom: 15px;}
	}
	.c-pagination-status-label{
		margin-top: 17px; font-size: 14px; line-height: 1.4;

		@media (max-width: @m){font-size: 13px;}
	}
	.c-pagination-items{
		display: flex; align-items: center; justify-content: center; margin-top: 17px;
		a, span{
			display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 40px; height: 40px; margin-left: -1px; background: @white; border: 1px solid @borderColor; font-size: 15px; line-height: 1.4; font-weight: 600; color: @textColor; text-align: center; text-decoration: none; position: relative; transition: border-color 0.3s, background 0.3s, color 0.3s, z-index 0.3s;
			&:hover{border-color: @blue; color: @blue; text-decoration: none; z-index: 11;}
		}
		.current-page{background: #f3f6fa; border-color: @blue; color: @blue; z-index: 1; pointer-events: none;}
		.pagination-mid-page{
			margin: 0 1px; padding-bottom: 8px; border: none;
			&:hover{color: @textColor; z-index: 0;}
		}
		.pagination-next-page, .pagination-prev-page{
			margin: 0 1px; border: none; font-size: 0; line-height: 0;
			&:before{.icon-arrow-down(); font: 12px/12px @fonti; color: @textColor; .rotate(-90deg); .transition(color);}
			&:hover{
				text-decoration: none;
				&:before{color: @blue;}
			}

			@media (max-width: @t){
				&:hover:before{color: @textColor;}
			}
		}
		.pagination-prev-page:before{.rotate(90deg);}

		@media (max-width: @t){
			a, span{
				width: 24px; height: 27px; font-size: 11px; z-index: 0;
				&:hover{border-color: @borderColor; color: @textColor; z-index: 0;}
			}
			span{
				z-index: 1;
				&:hover{border-color: @blue; color: @blue; z-index: 1;}
			}
		}
	}

	.c-empty{
		padding: 30px 0; font-size: 18px;

		@media (max-width: @m){padding: 20px 0; font-size: 14px;}
	}

	.c-filter-back{
		display: block; min-height: 35px; padding-top: 3px; padding-bottom: 14px; background: @white; border-bottom: 1px solid #E0E8EE; font-size: 15px; line-height: 1.3; font-weight: 600; position: sticky; top: 0; z-index: 11;
		&.link{
			padding-left: 15px; color: @blue; text-decoration: none;
			&:after{.icon-arrow-down(); font: 8px/8px @fonti; font-weight: 600; color: @blue; position: absolute; top: 9px; left: 0; .rotate(90deg); .transition(left);}
			@media (min-width: @t){
				&:hover{text-decoration: underline;}
			}
		}

		@media (max-width: @t){
			padding-top: 0; font-size: 14px;
			&.link{
				&:after{font-size: 7px; line-height: 7px; top: 6px;}
			}
		}
		@media (max-width: @m){display: none;}
	}


	//catalog header
	.c-desc {
		width: 100%;
		margin-bottom: 18px;
		position: relative;

		&.active {
			.c-desc-btn {
				display: inline-flex;
				position: relative;
				right: unset;
				top: unset;
				&:before {
					content: none;
				}
				& > span:after {
					.rotate(180deg);
				}
				.more {
					display: none;
				}
				.less {
					display: inline-block;
				}
			}
		}
	}
	.c-desc-content {
		display: -webkit-box;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		padding: 0;
		font-size: 16px;
		line-height: 1.4;
		position: relative;
		a {
			color: @blue;
			text-decoration: underline;
			&:hover {
				color: @blue;
				text-decoration: none;
			}
		}
		img {
			display: inline;
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
		}
		ul {
			list-style: none;
			display: inline;
			height: 0;
			margin: 0;
			padding: 0;
			& > li {
				display: inline;
				padding: 0;
				position: relative;
				&:before {
					.pseudo(12px, 1px);
					background: @blue;
					top: 14px;
					left: 0;
					z-index: 1;
				}
			}
		}
		ol {
			display: inline;
			height: 0;
			margin: 0;
			li {
				display: inline;
				padding: 0;
				margin: 0;
			}
		}
		h2,
		h3,
		h4 {
			display: inline;
			font-size: 20px;
			line-height: 1.2;
		}
		h5 {
			display: inline;
		}
		p,
		div {
			display: inline;
		}
		& > * {
			margin: 0;
			padding: 0;
		}

		&.active {
			display: block;
			overflow: initial;
			text-overflow: unset;
			-webkit-line-clamp: unset;
			-webkit-box-orient: unset;
			ul,
			ol,
			img {
				display: block;
			}
			ul {
				height: auto;
				margin: 0 0 15px 20px;
				& > li {
					display: block;
					padding: 2px 0 4px 25px;
				}
			}
			ol {
				height: auto;
				margin: 0 0 15px 38px;
				& > li {
					display: list-item;
					padding: 0 0 5px;
				}
			}
			h2,
			h3,
			h4,
			h5 {
				display: block;
				padding: 10px 0 5px;
			}
			div {
				display: block;
			}
			& > p {
				display: block;
				padding-bottom: 10px;
			}
		}

		@media (max-width: @t) {
			font-size: 15px;
			h2,
			h3,
			h4 {
				font-size: 18px;
			}
			&.active {
				ul {
					margin: 0 0 15px 0;
				}
				ol {
					margin-left: 25px;
				}
			}
		}
		@media (max-width: @m) {
			margin-bottom: 13px;
		}
	}
	.c-desc-btn {
		display: inline;
		margin: 0;
		padding: 0;
		background: @white;
		font-size: 15px;
		line-height: 1.4;
		color: @blue;
		font-weight: 600;
		position: absolute;
		right: 0;
		bottom: 1px;
		cursor: pointer;
		z-index: 1;
		&:before {
			.pseudo(auto,auto);
			background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 65%);
			top: 0;
			bottom: 0;
			right: 100%;
			left: -65px;
			z-index: -1;
		}
		& > span {
			padding-right: 13px;
			position: relative;
			&:after {
				.icon-arrow-down();
				font: 7px/7px @fonti;
				font-weight: 600;
				color: @blue;
				position: absolute;
				top: 8px;
				right: 0;
			}
		}
		.less {
			display: none;
		}
		@media (min-width: @t) {
			&:hover {
				text-decoration: underline;
			}
		}

		@media (max-width: @t) {
			top: 0;
			&:before {
				left: -45px;
			}
		}
		@media (max-width: @m) {
			top: 1px;
			& > span:after {
				top: 9px;
			}
		}
	}

	.c-promo-special {
		width: 100%;
		margin: 0 0 15px;
		position: relative;

		.swiper:not(.swiper-initialized) .swiper-slide:not(:first-child){overflow: hidden; height: 0; visibility: hidden;}
		.swiper-navigation{display: none;}
		&.not-single{
			min-height: 160px;
			.swiper-navigation{display: block;}
		}

		@media (max-width: @l){
			&.not-single{min-height: 60px;}
		}
		@media (max-width: @t) {
			.swiper-button-prev, .swiper-button-next{
				width: 50px; height: 50px; background: @white; border: none; border-radius: 100%; box-shadow: 0 20px 40px 0 rgba(1, 61, 112, 0.25);
				&:before{font-weight: 600; color: @blue;}
			}
			.swiper-button-next{right: 20px;}
			.swiper-button-prev{left: 20px;}
		}
		@media (max-width: @m){
			margin: 12px 0 5px;

			.swiper-button-prev, .swiper-button-next{
				background: transparent;
				&:before{color: @textColor; font-weight: normal;}
				&:after{.pseudo(auto,auto); background: @white; border-radius: 100%; top: 0; bottom: 0; left: 0; right: 0; opacity: 0.5;}
			}
			.swiper-button-next{right: 15px;}
			.swiper-button-prev{left: 15px;}
		}
		@media (max-width: @ms){
			&.not-single{min-height: 100px;}
		}
	}
	.c-promo-item-special {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		width: 100%;
		margin: 0;
		font-size: 0;
		line-height: 0;
		img{width: auto; height: auto; max-width: 100%; max-height: 100%;}
		@media (min-width: @t) {
			&.link:hover .c-promo-item-special-img:before {
				opacity: 1;
			}
		}
	}
	.c-promo-item-special-img {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		position: relative;
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}
		&:before {
			.pseudo(auto,auto);
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(32, 42, 51, 0.2);
			opacity: 0;
			.transition(opacity);
		}
		:deep(img) {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}
	}
	.c-promo-list-item {
		margin-bottom: 15px;
	}
	.c-promo-list-item-img {
		justify-content: flex-start;
	}

	//featured items slider
	.c-exclude-items {
		margin-bottom: 30px;
		position: relative;

		@media (max-width: @t) {
			margin-bottom: 15px;
		}
		@media (max-width: @m) {
			margin-top: 15px;
		}
	}
	.c-exclude-slider {
		display: block;
		position: relative;

		@media (max-width: @m) {
			width: calc(~'100% - -40px');
			margin: 0 0 0 -20px;
		}
		@media (max-width: @ms) {
			width: calc(~'100% - -30px');
			margin: 0 0 0 -15px;
		}
	}
	.c-exclude-slider2 {
		display: block;
		position: relative;
		@media (max-width: @m){
			.swiper-navigation{display: none;}
		}
		@media (max-width: @ms){width: calc(~"100% - -30px"); margin: 0 0 0 -15px;}
	}

	//toolbar
	.c-toolbar {
		display: flex;
		align-items: center;
		flex-grow: 1;
		padding: 15px 0;
		background: @white;
		position: sticky;
		top: 0;
		z-index: 111;
		.transition(all);

		@media (max-width: @t) {
			padding: 10px 0;
		}
		@media (max-width: @m) {
			justify-content: space-between;
		}
		@media (max-width: @ms) {
			z-index: 11;
		}
	}

	.s-extra-info-wrapper {
		display: none;

		@media (max-width: @m) {
			display: flex;
			justify-content: flex-end;
			margin: 2px 0 15px;
			.s-extra-info {
				display: flex;
			}
		}
	}
	.s-extra-info-container {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex-grow: 1;
		margin: 0 15px;

		@media (max-width: @m) {
			display: none;
			margin: 0;
		}
	}
	.s-extra-info {
		display: flex;
		align-items: center;
		font-size: 12px;
		line-height: 15px;
		color: @inputTextColor;
		font-weight: 600;
		text-decoration: underline;
		cursor: pointer;
		span {
			display: block;
			padding-left: 22px;
			position: relative;
			&:before {
				.icon-info();
				font: 14px/1 @fonti;
				color: @inputTextColor;
				position: absolute;
				left: 0;
			}
		}
		&:hover {
			text-decoration: none;
		}

		@media (max-width: @m) {
			display: none;
			justify-content: center;
			width: 49%;
			span:before {
				margin-top: 1px;
			}
		}
	}

	.cf-selectedprice input[type='checkbox'] + label,
	.cf-discount input[type='checkbox'] + label {
		display: flex; align-items: center; height: 50px; margin-right: 10px; padding: 15px 20px 15px 45px; background: @white; border: 1px solid @borderColor; font-size: 15px; line-height: 1.2; font-weight: 600; color: @textColor; position: relative; .transition(border-color);
		&:before{top: unset; left: 15px;}
		@media (min-width: @t) {
			&:hover{border-color: @hoverBorderColor;}
		}

		@media (max-width: @t){
			height: 44px; padding: 5px 15px 5px 45px; font-size: 12px;
			&:hover{color: @textColor;}
		}
		@media (max-width: @m){height: 53px; margin: 0; padding: 5px 15px 5px 45px; border: none; border-bottom: 1px solid @borderColor; font-size: 15px;}
	}
	.cf-selectedprice, .cf-discount{
		@media (max-width: @m){display: none;}
	}

	.sort {
		display: flex;
		flex-shrink: 0;
		width: 205px;
		position: relative;
		&:before {
			.icon-compare();
			font: 13px/13px @fonti;
			font-weight: 600;
			color: @blue;
			position: absolute;
			top: 20px;
			left: 20px;
			z-index: -1;
		}
		select {
			height: 50px;
			padding-left: 43px;
			padding-right: 35px;
			background: url(assets/images/arrow-down.svg) no-repeat;
			background-size: 8px;
			background-position: right 18px top 21px;
			font-size: 15px;
			font-weight: 600;
		}

		@media (max-width: @t) {
			width: 170px;
			&:before {
				top: 17px;
				left: 15px;
			}
			select {
				height: 44px;
				padding-left: 40px;
				padding-right: 30px;
				font-size: 12px;
				background-position: right 15px top 19px;
			}
		}
		@media (max-width: @m) {
			width: 49%;
			margin: 0;
			select {
				height: 44px;
				font-size: 14px;
				background-size: 7px;
				background-position: right 12px top 19px;
			}
		}
	}
	.c-toolbar-btn-filters {
		display: none;

		@media (max-width: @m) {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 49%;
			height: 44px;
			background: linear-gradient(270deg, #00b0de 0%, #0078ba 100%);
			font-size: 14px;
			line-height: 1.3;
			font-weight: 600;
			color: @white;
			& > span {
				padding-left: 25px;
				position: relative;
				&:before {
					.icon-filter();
					font: 15px/15px @fonti;
					color: @white;
					position: absolute;
					left: 0;
					top: 2px;
				}
			}
		}
	}

	.s-header-categories-wrapper{
		display: flex; align-items: center; justify-content: center; width: 1480px; margin: 0 auto; padding: 24px 0; font-size: 15px; line-height: 1.5; font-weight: 600;

		@media (max-width: @l){width: auto; margin: 0 70px;}
		@media (max-width: @t){margin: 0 30px;}
		@media (max-width: @m){display: block; margin: 0 15px; padding: 15px 0; font-size: 14px;}
	}
	.s-header-categories{
		display: flex; align-items: center; flex-wrap: wrap;

		@media (max-width: @m){
			flex-wrap: unset; align-items: unset; justify-content: unset; width: calc(~"100% - -30px"); margin: 5px 0 0 -15px; padding: 0 15px; opacity: 1; overflow-y: hidden; white-space: normal; -webkit-overflow-scrolling: touch; position: relative;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.s-header-category{
		display: block; margin: 5px 10px; color: @blue; text-decoration: none; position: relative;
		&:before{.pseudo(1px,17px); background: @borderColor; right: -10px; top: 3px;}
		&:last-child{
			margin-right: 0;
			&:before{content: none;}
		}
		&:hover{color: @blue; text-decoration: underline;}

		@media (max-width: @t){
			&:hover{text-decoration: none;}
		}
		@media (max-width: @m){
			margin: 0 10px 0 0; padding-right: 10px; white-space: nowrap;
			&:before{right: 0;}
		}
	}
</style>

<style lang="less">
	.c-exclude-slider {
		.swiper-slide {
			display: flex;
		}
		.swiper-button {
			top: 130px;
		}

		@media (max-width: @m) {
			.swiper-wrapper {
				display: flex !important;
				overflow-y: hidden;
				white-space: normal;
				position: relative;
				&::-webkit-scrollbar {
					display: none;
				}
				&::-webkit-scrollbar-thumb {
					display: none;
				}
			}
			.swiper-slide {
				flex-shrink: unset;
				min-width: 30%;
				width: 30%;
				margin-right: 20px;
				white-space: normal;
			}
		}
		@media (max-width: @ms) {
			.swiper-slide {
				min-width: 42%;
				width: 42%;
			}
		}
	}
	.c-exclude-slider2 {
		.swiper-slide {
			display: flex;
		}
		.swiper-button {
			top: 75px;
		}

		@media (max-width: @t) {
			.swiper-button {
				top: 90px;
			}
		}
		@media (max-width: @m) {
			.swiper-wrapper {
				display: flex !important;
				overflow-y: hidden;
				white-space: normal;
				position: relative;
				&::-webkit-scrollbar {
					display: none;
				}
				&::-webkit-scrollbar-thumb {
					display: none;
				}
			}
			.swiper-slide {
				flex-shrink: unset;
				min-width: 30%;
				width: 30%;
				margin-right: 20px;
				white-space: normal;
			}
		}
		@media (max-width: @ms) {
			.swiper-wrapper {
				padding: 0 0 0 15px !important;
			}
			.swiper-slide {
				min-width: 80%;
				width: 80%;
				margin: 0 -1px 0 0;
			}
		}
	}
</style>
