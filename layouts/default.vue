<template>
	<Meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
	<Meta name="robots" content="noindex" v-if="route.path == '/srecka-2025/'" />
	<div class="page-wrapper" @scroll="onScroll">
		<CmsPageLoadingIndicator />
		<Gdpr />
		<NuxtLink to="/odprodaja-na-vseh-oddelkih/">1</NuxtLink>
		<slot name="pop_up">
			<ClientOnly>
				<CmsPopup />
			</ClientOnly>
		</slot>
		<CmsHeader :landingNavActive="landingNavActive" />

		<slot />

		<ClientOnly>
			<NewsletterSignup />
		</ClientOnly>

		<CmsFooter />

		<ClientOnly>
			<CmsEu />
			<CmsFlyout :data="bus.data" />
			<WebshopAddToCartModal />
			<LazyCmsModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" v-slot="{item}">
				<h1 v-if="item?.title" v-html="item.title"></h1>
				<div v-if="item?.content" v-html="item.content"></div>
			</LazyCmsModal>
			<GdprVerificationXXX />
			<div @click="toTop()" class="to-top" :class="{'active': showToTopButton}"></div>
			<span @click="chatActivate" class="el-chat" :class="{'loading': chatLoading, 'active': !chatWidget && chatActive && chatLocalStorage == null, 'special': chatSpecial}" ref="chatEl"></span>
		</ClientOnly>
	</div>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const page = usePage();
	const info = useInfo();
	const auth = useAuth();
	const labels = useLabels();
	const {bus} = useEventBus();
	const modal = useModal();
	const gtm = useGtmBB();
	let showToTopButton = ref(false);
	let modalContent = ref(null);
	const { creation } = useCheckout();
	const {addScript} = useMeta();
	const route = useRoute();
	const keycloak = useKeycloak();

	const gdprCookie = useCookie('gdpr_cookie');
	watch(
		gdprCookie,
		data => gtm.gtmTrackGdpr(data),
		{immediate: true}
	);

	onMounted(() => {
		addScript({
			key: 'tiktok',
			innerHTML: `!function (w, d, t) {
						w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(
						var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var r="https://analytics.tiktok.com/i18n/pixel/events.js",o=n&&n.partner;ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=r,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script")
						;n.type="text/javascript",n.async=!0,n.src=r+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
						ttq.load('CQQRI5JC77U53M1BKAR0');
						ttq.page();
						}(window, document, 'ttq');`,
			gdpr: 'marketing',
			onRevokeConsent() {
				if (typeof window.ttq !== 'undefined') {
					ttq.revokeConsent();
					ttq.disableCookie();
				}
			},
			onGrantConsent(){
				if (typeof window.ttq !== 'undefined') {
					ttq.grantConsent();
					ttq.enableCookie();
				}
			}
		});
	});

	//rwd
	const {onMediaQuery} = useDom();
	const {matches: mobileSmallBreakpoint} = onMediaQuery({query: '(max-width: 760px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 980px)'});
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	provide('rwd', {tabletBreakpoint, mobileBreakpoint, mobileSmallBreakpoint});

	//chat values
	const {chatWidget, chatActive, chatSpecial, chatLoading, chatScrollTimer } = useChat();
	const chatEl = ref();
	const chatLocalStorage = ref(null);

	//chat on click activate servise
	function chatActivate() {
		if (chatLocalStorage.value == null) {
			localStorage.setItem('isChat', 1);
			chatLoading.value = 1;

			(function (I, n, f, o, b, i, p) {
			I[b] = I[b] || function () {
				(I[b].q = I[b].q || []).push(arguments);
			};
			I[b].t = 1 * new Date();
			i = n.createElement(f);
			i.async = 1;
			i.src = o;
			p = n.getElementsByTagName(f)[0];
			p.parentNode.insertBefore(i, p);
			})(window, document, 'script', 'https://livechat.infobip.com/widget.js', 'liveChat');

			liveChat('init', '11a26f53-7321-4938-81d9-6f8dbedbf94f');

			const intervalId = setInterval(() => {
				const ibWidget = document.querySelector('.ib-widget-wrapper');
				const ibButtonMessaging = document.getElementById('ib-button-messaging');
				if (ibWidget) {
					if (ibButtonMessaging && ibWidget && getComputedStyle(ibWidget).visibility === 'hidden') {
						ibButtonMessaging.click();
					}
					chatActive.value = true;
					chatWidget.value = true;
					chatLoading.value = 0;

					const scroll = document.documentElement.scrollTop;
					const ibButtonMessagingEl = document.getElementById('ib-button-messaging');
					const ibEyeCatcherEl = document.getElementById('eye-catcher-container');

					if (scroll > 270) {
						if(ibButtonMessagingEl) {ibButtonMessagingEl.classList.add('special');}
						if(ibEyeCatcherEl) {ibEyeCatcherEl.classList.add('special');}
					} else {
						if(ibButtonMessagingEl) {ibButtonMessagingEl.classList.remove('special');}
						if(ibEyeCatcherEl) {ibEyeCatcherEl.classList.remove('special');}
					}

					clearInterval(intervalId);
				}
			}, 1000);

			setTimeout(() => {
				chatLoading.value = 0;
				clearInterval(intervalId);
			}, 16000);

			return false;
		} else {
			chatActive.value = true;
			chatWidget.value = true;

			(function (I, n, f, o, b, i, p) {
			I[b] = I[b] || function () {
				(I[b].q = I[b].q || []).push(arguments);
			};
			I[b].t = 1 * new Date();
			i = n.createElement(f);
			i.async = 1;
			i.src = o;
			p = n.getElementsByTagName(f)[0];
			p.parentNode.insertBefore(i, p);
			})(window, document, 'script', 'https://livechat.infobip.com/widget.js', 'liveChat');

			liveChat('init', '11a26f53-7321-4938-81d9-6f8dbedbf94f');

			const scroll = document.documentElement.scrollTop;
			const ibButtonMessagingEl = document.getElementById('ib-button-messaging');
			const ibEyeCatcherEl = document.getElementById('eye-catcher-container');

			if (scroll > 270) {
				if(ibButtonMessagingEl) {ibButtonMessagingEl.classList.add('special');}
				if(ibEyeCatcherEl) {ibEyeCatcherEl.classList.add('special');}
			} else {
				if(ibButtonMessagingEl) {ibButtonMessagingEl.classList.remove('special');}
				if(ibEyeCatcherEl) {ibEyeCatcherEl.classList.remove('special');}
			}
		}
	}

	//to top
	function toTop() {
		window.scrollTo({
			top: 0,
			behavior: 'smooth',
		});
	}
	const lastScrollTop = ref(0);
	const landingNavActive = ref(null)
	const onScroll = () => {
		//fixed header
		if (document.body.classList.contains('page-catalog-lvl2')) {
			const scrollTop = window.scrollY;
			if (Math.abs(lastScrollTop.value - scrollTop) <= 1) {
				return;
			}

			if (scrollTop > lastScrollTop.value) {
				document.body.classList.remove('fixed-header');
			} else {
				document.body.classList.add('fixed-header');
			}

			if (scrollTop < 200) {
				document.body.classList.remove('fixed-header');
			}

			lastScrollTop.value = scrollTop;
		} else {
			const scroll = document.documentElement.scrollTop;

			if (scroll > 145) {
				document.body.classList.add('fixed-header');
			} else {
				document.body.classList.remove('fixed-header');
			}
		}

		//landing nav
		if(document && document.body.classList.contains('page-landing-menu')) {
			//active tab
			const landingElements = document.querySelectorAll('.landing-wrapper>div')
			if(landingElements.length > 0) {
				landingElements.forEach((landingElement) => {
					const { top } = landingElement.getBoundingClientRect()
					if (top <= 125) {
						landingNavActive.value = landingElement.id;
					}
				})
			}
		}

		//to top button / chat button
		const scroll = document.documentElement.scrollTop;
		const ibButtonMessagingEl = document.getElementById('ib-button-messaging');
		const ibEyeCatcherEl = document.getElementById('eye-catcher-container');

		if (scroll > 270) {
			showToTopButton.value = true;
			chatSpecial.value = true;

			if(ibButtonMessagingEl) {
				ibButtonMessagingEl.classList.add('special');
			}
			if(ibEyeCatcherEl) {
				ibEyeCatcherEl.classList.add('special');
			}
		} else {
			showToTopButton.value = false;
			chatSpecial.value = false;

			if(ibButtonMessagingEl) {
				ibButtonMessagingEl.classList.remove('special');
			}
			if(ibEyeCatcherEl) {
				ibEyeCatcherEl.classList.remove('special');
			}
		}

		//chat on scroll
		if (chatActive.value && chatLocalStorage.value == null && chatEl.value && !document.body.classList.contains('page-cart') && !document.body.classList.contains('page-checkout')) {
			chatActive.value = false;
			clearTimeout(chatScrollTimer.value);
			chatScrollTimer.value = setTimeout(() => {
				chatActive.value = true;
			}, 1600);
		}
	};

	//order creation in progress
	const handleOrderInCreation = () => {
		const storedValue = localStorage.getItem('creation');
		creation.value = storedValue ? JSON.parse(storedValue) : false;
	};

	onMounted(async () => {
		//chat holiday
		const chatHoliday = [info.getInfo('chat_holiday')];
		if(chatHoliday.length) {
			let chatHolidayData = chatHoliday[0].split("\n");
			chatHolidayData.sort();

			const currentDate = new Date().toISOString().slice(0, 10);
			const chatActiveData = !chatHolidayData.includes(currentDate);

			chatActive.value = chatActiveData;
		}

		//chat holiday
		const chatWorkingHour = [info.getInfo('chat_working_hour')];
		if(chatWorkingHour.length && chatActive.value) {
			const currentDay = new Date().toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase();
			const currentTime = new Date().getTime();
			const workingHours = chatWorkingHour[0].split('\n').reduce((result, day) => {
				const [dayName, hours] = day.split('|');
				result[dayName] = hours;
				return result;
			}, {});

			if (workingHours.hasOwnProperty(currentDay)) {
				const [startHour, endHour] = workingHours[currentDay].split('-').map(time => {
				const [hour, minute] = time.split(':');
					return new Date().setHours(parseInt(hour), parseInt(minute), 0, 0);
				});

				if(currentTime >= startHour && currentTime <= endHour) {
					chatActive.value = true;
				} else {
					chatActive.value = false;
				}
			}
		}

		//chat show/hide
		if (chatActive.value && !document.body.classList.contains('page-cart') && !document.body.classList.contains('page-checkout')) {
			chatLocalStorage.value = localStorage ? localStorage.getItem('isChat') : null;
			if(chatWidget.value == false && chatLocalStorage.value == null) {
				setTimeout(() => {
					chatActive.value = true;
				}, 1000);
			}
			if(chatWidget.value == false && chatLocalStorage.value != null) {
				chatActivate()
			}
		}

		//add event listener
		window.addEventListener('scroll', onScroll);

		//keycloak
		const isAuthTemplate = route.meta.controller != 'webshop' && ['AuthLogin', 'AuthLogout'].includes(route.meta.template);
		const isExcludedAction = ['payment_new', 'payment_create', 'payment_canceled', 'payment_form_autosubmit', 'create_order', 'payment_on_hold', 'thank_you', 'failed_order', 'failed_payment', 'view_additionalpayment', 'additionalpayments_thank_you', 'additionalpayments_disabled', 'additionalpayments_failed_payment'].includes(route.meta.action);
		if (!isExcludedAction && !isAuthTemplate) {
			keycloak.checkKeycloak();
		}

		//order creation in progress
		window.addEventListener('storage', handleOrderInCreation);

		//gtm referrer
		const gtm = useGtmBB();
		const config = useAppConfig();
		let previousUrl = null;
		watch(
			() => route.fullPath,
			(newValue, oldValue) => {
				if(oldValue) {
					const referrerValue = config?.host + oldValue;
					gtm.sendReferral(referrerValue);
					previousUrl = newValue;
				} else if(document && document.referrer) {
					const referrerValue = document.referrer;
					gtm.sendReferral(referrerValue);
				}
			},
			{ immediate: true }
		);
	});
	onUnmounted(() => {
		landingNavActive.value = false;
		window.removeEventListener('storage', handleOrderInCreation);
	});
</script>

<style lang="less" scoped>
	.page-catalog-detail{
		.to-top.active{bottom: 105px;}
		.el-chat.special{bottom: 185px;}
		.eu{padding-bottom: 120px;}

		@media (max-width: @m){
			.el-chat{bottom: 75px;}
		}
	}
	.page-webshop{
		.ib-button-messaging, .ib-widget-wrapper, .el-chat{display: none!important;}
	}
	.page-auth-mail-confirmation-status{
		.nw{display: none;}
		.footer{border-top: 1px solid @borderColor;}
	}

	.to-top{
		width: 60px; height: 60px; border-radius: 100%; position: fixed; display: block; background: @white; text-align: center; text-decoration: none; box-shadow: 0 0 40px 0 rgba(1, 61, 112, 0.25); .transition(all); z-index: 99999; opacity: 0; visibility: hidden; cursor: pointer;
		&:before{.pseudo(auto,auto); border-radius: 100%; left: -1px; right: -1px; bottom: -1px; top: -1px; background: linear-gradient(315deg, #0078b4 0%, #0050a0 100%); z-index: -1; opacity: 0; .transition(opacity);}
		&:after{.icon-arrow-down(); font: 16px/60px @fonti; color: @blue; display: block; transform: rotate(180deg); .transition(color);}
		&:hover{
			text-decoration: none;
			&:before{opacity: 1;}
			&:after{color: @white;}
		}
		&.active{
			bottom: 50px; right: 50px; opacity: 1; visibility: visible; text-decoration: none !important;
			&.special{bottom: 105px;}
		}

		@media (max-width: @m){display: none !important;}
	}

	//chat
	@keyframes bounce {
		0% {transform: translateX(0);}
		50% {transform: translateX(-10px);}
		100% {transform: translateX(0);}
	}

	.el-chat{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 60px; height: 60px; background: url(/images/chat-new1.webp) no-repeat; background-size: contain; border-radius: 100%; font-size: 10px; line-height: 1.4; font-weight: 600; color: @textColor; text-decoration: none; text-transform: uppercase; position: fixed; right: 50px; bottom: 50px; box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12); cursor: pointer; z-index: 1111; transform: translateX(100px); opacity: 0; transition: bottom 0.8s, transform 0.8s, opacity 0.8s;
		&.active{transform: none; opacity: 1;}
		&.special{bottom: 130px;}
		&.hidden{display: none!important;}
		&.animation {animation: bounce 1s 2 linear;}
		&.loading{
			transform: none; opacity: 1;
			&:before{.pseudo(auto,auto); background: rgba(0,26,52,0.2) url(/images/loader.svg); background-size: contain; border-radius: 100%; position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 111;}
		}

		@media (max-width: @m){bottom: 50px; right: 15px;}
	}
</style>
